package com.wtb.photocc_android.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Done
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.wtb.photocc_android.data.export.ExportStatus
import com.wtb.photocc_android.data.export.ExportTask

/**
 * 导出进度对话框
 */
@Composable
fun ExportProgressDialog(
    exportTask: ExportTask?,
    onCancel: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    if (exportTask != null) {
        Dialog(
            onDismissRequest = {
                if (!exportTask.isInProgress()) {
                    onDismiss()
                }
            },
            properties = DialogProperties(
                dismissOnBackPress = !exportTask.isInProgress(),
                dismissOnClickOutside = !exportTask.isInProgress()
            )
        ) {
            Card(
                modifier = modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
            ) {
                Column(
                    modifier = Modifier.padding(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // 标题
                    Text(
                        text = when (exportTask.status) {
                            ExportStatus.PENDING -> "准备导出"
                            ExportStatus.PREPARING -> "准备中"
                            ExportStatus.PROCESSING -> "导出中"
                            ExportStatus.COMPLETED -> "导出完成"
                            ExportStatus.FAILED -> "导出失败"
                            ExportStatus.CANCELLED -> "导出已取消"
                        },
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 状态图标和进度
                    when (exportTask.status) {
                        ExportStatus.PENDING, ExportStatus.PREPARING, ExportStatus.PROCESSING -> {
                            CircularProgressIndicator(
                                progress = { exportTask.progress / 100f },
                                modifier = Modifier.size(64.dp),
                                strokeWidth = 6.dp
                            )
                            
                            Spacer(modifier = Modifier.height(16.dp))
                            
                            Text(
                                text = "${exportTask.progress.toInt()}%",
                                style = MaterialTheme.typography.titleLarge,
                                fontWeight = FontWeight.Bold
                            )
                        }
                        
                        ExportStatus.COMPLETED -> {
                            Icon(
                                imageVector = Icons.Default.Done,
                                contentDescription = "完成",
                                modifier = Modifier.size(64.dp),
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }

                        ExportStatus.FAILED -> {
                            Icon(
                                imageVector = Icons.Default.Warning,
                                contentDescription = "失败",
                                modifier = Modifier.size(64.dp),
                                tint = MaterialTheme.colorScheme.error
                            )
                        }

                        ExportStatus.CANCELLED -> {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "取消",
                                modifier = Modifier.size(64.dp),
                                tint = MaterialTheme.colorScheme.outline
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // 状态消息
                    if (exportTask.message.isNotBlank()) {
                        Text(
                            text = exportTask.message,
                            style = MaterialTheme.typography.bodyMedium,
                            textAlign = TextAlign.Center,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        
                        Spacer(modifier = Modifier.height(16.dp))
                    }
                    
                    // 错误信息
                    if (exportTask.status == ExportStatus.FAILED && !exportTask.error.isNullOrBlank()) {
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(
                                containerColor = MaterialTheme.colorScheme.errorContainer
                            )
                        ) {
                            Text(
                                text = exportTask.error,
                                modifier = Modifier.padding(12.dp),
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onErrorContainer
                            )
                        }
                        
                        Spacer(modifier = Modifier.height(16.dp))
                    }
                    
                    // 操作按钮
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        when (exportTask.status) {
                            ExportStatus.PENDING, ExportStatus.PREPARING, ExportStatus.PROCESSING -> {
                                Button(
                                    onClick = onCancel,
                                    modifier = Modifier.fillMaxWidth(),
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = MaterialTheme.colorScheme.error
                                    )
                                ) {
                                    Text("取消导出")
                                }
                            }
                            
                            ExportStatus.COMPLETED, ExportStatus.FAILED, ExportStatus.CANCELLED -> {
                                Button(
                                    onClick = onDismiss,
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    Text("确定")
                                }
                            }
                        }
                    }
                    
                    // 导出信息
                    if (exportTask.status != ExportStatus.PENDING) {
                        Spacer(modifier = Modifier.height(12.dp))
                        
                        Column {
                            Text(
                                text = "导出格式: ${exportTask.config.format.displayName}",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            
                            Text(
                                text = "图片数量: ${exportTask.images.size}",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            
                            if (exportTask.config.fileName.isNotBlank()) {
                                Text(
                                    text = "文件名: ${exportTask.config.getFullFileName()}",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                            
                            // 显示耗时
                            exportTask.getDuration()?.let { duration ->
                                Text(
                                    text = "耗时: ${duration / 1000.0}秒",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}
