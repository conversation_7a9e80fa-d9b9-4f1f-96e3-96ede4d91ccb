package com.wtb.photocc_android.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Send
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.wtb.photocc_android.data.export.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ExportConfigScreen(
    initialConfig: ExportConfig,
    onConfigChange: (ExportConfig) -> Unit,
    onStartExport: () -> Unit,
    onNavigateToLayout: () -> Unit,
    onNavigateBack: () -> Unit,
    isExporting: Boolean = false,
    modifier: Modifier = Modifier
) {
    var config by remember { mutableStateOf(initialConfig) }
    
    LaunchedEffect(config) {
        onConfigChange(config)
    }
    
    Column(
        modifier = modifier.fillMaxSize()
    ) {
        // 顶部栏
        TopAppBar(
            title = { Text("导出设置") },
            navigationIcon = {
                IconButton(onClick = onNavigateBack) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                }
            },
            actions = {
                IconButton(
                    onClick = onStartExport,
                    enabled = config.isValid() && !isExporting
                ) {
                    if (isExporting) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(24.dp),
                            strokeWidth = 2.dp
                        )
                    } else {
                        Icon(Icons.Default.Send, contentDescription = "开始导出")
                    }
                }
            }
        )
        
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 格式信息
            item {
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "导出格式",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = config.format.displayName,
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }
            }
            
            // 文件名设置
            item {
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "文件名",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        OutlinedTextField(
                            value = config.fileName,
                            onValueChange = { fileName ->
                                config = config.copy(fileName = fileName)
                            },
                            label = { Text("输入文件名") },
                            placeholder = { Text("export_${System.currentTimeMillis()}") },
                            modifier = Modifier.fillMaxWidth(),
                            singleLine = true,
                            enabled = !isExporting
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = "完整文件名: ${config.getFullFileName()}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
            
            // 排版设置（仅对需要排版的格式显示）
            if (config.needsLayout()) {
                item {
                    Card(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Column(
                                    modifier = Modifier.weight(1f)
                                ) {
                                    Text(
                                        text = "排版设置",
                                        style = MaterialTheme.typography.titleMedium,
                                        fontWeight = FontWeight.Bold
                                    )
                                    Spacer(modifier = Modifier.height(4.dp))
                                    Text(
                                        text = config.layoutConfig?.let { layoutConfig ->
                                            "${layoutConfig.template.displayName} - ${layoutConfig.getActualColumns()}列"
                                        } ?: "未设置",
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }
                                
                                Button(
                                    onClick = onNavigateToLayout,
                                    enabled = !isExporting
                                ) {
                                    Text("设置排版")
                                }
                            }
                        }
                    }
                }
            }
            
            // 质量设置
            item {
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "图片质量",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "质量: ${config.quality}%",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Slider(
                            value = config.quality.toFloat(),
                            onValueChange = { value ->
                                config = config.copy(quality = value.toInt())
                            },
                            valueRange = 10f..100f,
                            enabled = !isExporting
                        )
                    }
                }
            }
            
            // 尺寸设置
            item {
                Card(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "最大尺寸",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = "宽度: ${config.maxWidth}px",
                                    style = MaterialTheme.typography.bodyMedium
                                )
                                Slider(
                                    value = config.maxWidth.toFloat(),
                                    onValueChange = { value ->
                                        config = config.copy(maxWidth = value.toInt())
                                    },
                                    valueRange = 512f..4096f,
                                    enabled = !isExporting
                                )
                            }
                            
                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = "高度: ${config.maxHeight}px",
                                    style = MaterialTheme.typography.bodyMedium
                                )
                                Slider(
                                    value = config.maxHeight.toFloat(),
                                    onValueChange = { value ->
                                        config = config.copy(maxHeight = value.toInt())
                                    },
                                    valueRange = 512f..4096f,
                                    enabled = !isExporting
                                )
                            }
                        }
                    }
                }
            }
            
            // ZIP压缩级别（仅对ZIP格式显示）
            if (config.format == ExportFormat.ZIP) {
                item {
                    Card(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = "压缩级别",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Bold
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "级别: ${config.compressionLevel} (0=无压缩, 9=最大压缩)",
                                style = MaterialTheme.typography.bodyMedium
                            )
                            Slider(
                                value = config.compressionLevel.toFloat(),
                                onValueChange = { value ->
                                    config = config.copy(compressionLevel = value.toInt())
                                },
                                valueRange = 0f..9f,
                                steps = 8,
                                enabled = !isExporting
                            )
                        }
                    }
                }
            }
            
            // 开始导出按钮
            item {
                Button(
                    onClick = onStartExport,
                    modifier = Modifier.fillMaxWidth(),
                    enabled = config.isValid() && !isExporting
                ) {
                    if (isExporting) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            strokeWidth = 2.dp,
                            color = MaterialTheme.colorScheme.onPrimary
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("导出中...")
                    } else {
                        Text("开始导出")
                    }
                }
            }
        }
    }
}
