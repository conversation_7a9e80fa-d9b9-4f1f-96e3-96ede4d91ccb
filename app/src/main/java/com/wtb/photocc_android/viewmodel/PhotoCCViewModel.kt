package com.wtb.photocc_android.viewmodel

import android.app.Application
import android.content.Intent
import android.net.Uri
import androidx.activity.result.ActivityResultLauncher
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.wtb.photocc_android.data.Session
import com.wtb.photocc_android.data.ViewMode
import com.wtb.photocc_android.data.export.ExportConfig
import com.wtb.photocc_android.data.export.ExportTask
import com.wtb.photocc_android.data.export.LayoutConfig
import com.wtb.photocc_android.export.ExportManager
import com.wtb.photocc_android.repository.SessionManager
import com.wtb.photocc_android.utils.DirectoryInfo
import com.wtb.photocc_android.utils.DirectorySelector
import com.wtb.photocc_android.utils.ImageScanner
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import java.io.OutputStream

class PhotoCCViewModel(application: Application) : AndroidViewModel(application) {

    private val imageScanner = ImageScanner(application)
    private val sessionManager = SessionManager(application, imageScanner)
    private val directorySelector = DirectorySelector(
        context = application,
        onDirectorySelected = ::onDirectorySelected,
        onError = ::onDirectorySelectorError
    )
    private val exportManager = ExportManager(application)
    
    // UI 状态
    var sessions by mutableStateOf<List<Session>>(emptyList())
        private set
    
    var currentSession by mutableStateOf<Session?>(null)
        private set
    
    var selectedDirectories by mutableStateOf<List<DirectoryInfo>>(emptyList())
        private set
    
    var sessionName by mutableStateOf("")
        private set
    
    var isCreatingSession by mutableStateOf(false)
        private set
    
    var errorMessage by mutableStateOf<String?>(null)
        private set

    // 导出相关状态
    var currentExportConfig by mutableStateOf<ExportConfig?>(null)
        private set

    var currentLayoutConfig by mutableStateOf<LayoutConfig?>(null)
        private set

    // 导出任务状态
    val currentExportTask: StateFlow<ExportTask?> = exportManager.currentTask
    
    init {
        loadSessions()
        loadCurrentSession()
    }
    
    /**
     * 注册目录选择器
     */
    fun registerDirectoryLauncher(launcher: ActivityResultLauncher<Intent>) {
        directorySelector.registerLauncher(launcher)
    }
    
    /**
     * 处理目录选择结果
     */
    fun handleDirectoryResult(uri: Uri?) {
        directorySelector.handleResult(uri)
    }
    
    /**
     * 加载所有会话
     */
    private fun loadSessions() {
        viewModelScope.launch {
            sessionManager.getAllSessions().collect { sessionList ->
                sessions = sessionList
            }
        }
    }
    
    /**
     * 加载当前会话
     */
    private fun loadCurrentSession() {
        viewModelScope.launch {
            sessionManager.getCurrentSession().collect { session ->
                currentSession = session
            }
        }
    }
    
    /**
     * 选择目录
     */
    fun selectDirectory() {
        directorySelector.selectDirectory()
    }
    
    /**
     * 目录选择回调
     */
    private fun onDirectorySelected(uri: Uri) {
        viewModelScope.launch {
            val directoryInfo = imageScanner.getDirectoryInfo(uri)
            if (directoryInfo != null) {
                val updatedList = selectedDirectories.toMutableList()
                // 检查是否已经存在
                if (!updatedList.any { it.uri == directoryInfo.uri }) {
                    updatedList.add(directoryInfo)
                    selectedDirectories = updatedList
                }
            }
        }
    }
    
    /**
     * 目录选择错误回调
     */
    private fun onDirectorySelectorError(error: String) {
        errorMessage = error
    }
    
    /**
     * 移除目录
     */
    fun removeDirectory(directory: DirectoryInfo) {
        selectedDirectories = selectedDirectories.filter { it.uri != directory.uri }
    }
    
    /**
     * 更新会话名称
     */
    fun updateSessionName(name: String) {
        sessionName = name
    }
    
    /**
     * 创建会话
     */
    fun createSession(onSuccess: (String) -> Unit) {
        if (selectedDirectories.isEmpty() || sessionName.isBlank()) return
        
        viewModelScope.launch {
            isCreatingSession = true
            
            val result = sessionManager.createSession(
                name = sessionName,
                directoryUris = selectedDirectories.map { it.uri }
            )
            
            result.onSuccess { session ->
                clearDirectorySelection()
                onSuccess(session.id)
            }.onFailure { error ->
                errorMessage = "创建会话失败: ${error.message}"
            }
            
            isCreatingSession = false
        }
    }
    
    /**
     * 清除目录选择
     */
    fun clearDirectorySelection() {
        selectedDirectories = emptyList()
        sessionName = ""
    }
    
    /**
     * 设置当前会话
     */
    fun selectCurrentSession(session: Session) {
        viewModelScope.launch {
            sessionManager.setCurrentSession(session)
        }
    }
    
    /**
     * 删除会话
     */
    fun deleteSession(sessionId: String) {
        viewModelScope.launch {
            sessionManager.deleteSession(sessionId)
        }
    }
    
    /**
     * 刷新会话
     */
    fun refreshSession(sessionId: String) {
        viewModelScope.launch {
            sessionManager.refreshSessionImages(sessionId)
        }
    }
    
    /**
     * 更新查看模式
     */
    fun updateViewMode(viewMode: ViewMode) {
        viewModelScope.launch {
            sessionManager.updateCurrentSession { session ->
                session.updateViewMode(viewMode)
            }
        }
    }
    
    /**
     * 更新当前图片索引
     */
    fun updateCurrentImageIndex(index: Int) {
        viewModelScope.launch {
            sessionManager.updateCurrentSession { session ->
                session.updateCurrentImageIndex(index)
            }
        }
    }
    
    /**
     * 清除错误消息
     */
    fun clearError() {
        errorMessage = null
    }

    // ========== 导出相关方法 ==========

    /**
     * 更新导出配置
     */
    fun updateExportConfig(config: ExportConfig) {
        currentExportConfig = config
    }

    /**
     * 更新排版配置
     */
    fun updateLayoutConfig(layoutConfig: LayoutConfig) {
        currentLayoutConfig = layoutConfig
        currentExportConfig?.let { config ->
            currentExportConfig = config.copy(layoutConfig = layoutConfig)
        }
    }

    /**
     * 开始导出
     */
    fun startExport(outputStream: OutputStream, onComplete: (Result<String>) -> Unit) {
        val config = currentExportConfig
        val session = currentSession

        if (config == null || session == null) {
            onComplete(Result.failure(Exception("导出配置或会话为空")))
            return
        }

        if (!config.isValid()) {
            onComplete(Result.failure(Exception("导出配置无效")))
            return
        }

        viewModelScope.launch {
            val task = ExportTask(
                config = config,
                images = session.images
            )

            val result = exportManager.startExport(task, outputStream)
            onComplete(result)
        }
    }

    /**
     * 取消导出
     */
    fun cancelExport() {
        exportManager.cancelExport()
    }

    /**
     * 清除导出任务
     */
    fun clearExportTask() {
        exportManager.clearCurrentTask()
    }

    /**
     * 检查是否正在导出
     */
    fun isExporting(): Boolean {
        return exportManager.isExporting()
    }
}
