# PhotoCC Android

一个基于 Jetpack Compose 的 Android 图片浏览应用，使用 Storage Access Framework (SAF) 获取目录权限，支持多目录浏览和多种显示模式。

## 功能特性

### 核心功能
- **目录选择**: 使用 SAF 选择一个或多个目录
- **会话管理**: 创建、保存、加载和管理浏览会话
- **图片浏览**: 支持单列、双列、多列显示模式
- **多格式导出**: 支持 PDF、单张大图、HTML、ZIP、EPUB 等格式导出
- **自定义排版**: 支持多种排版模板和自定义配置
- **权限管理**: 自动处理存储权限请求
- **数据持久化**: 使用 DataStore 保存会话数据

### 技术特性
- **Jetpack Compose**: 现代化的 UI 框架
- **Storage Access Framework**: 安全的文件访问
- **Navigation Compose**: 声明式导航
- **Coil**: 高效的图片加载
- **Kotlin Serialization**: 数据序列化
- **ViewModel**: MVVM 架构模式
- **iText PDF**: PDF 文档生成
- **ZIP4J**: ZIP 文件压缩
- **Canvas API**: 图片合成和处理

## 项目结构

```
app/src/main/java/com/wtb/photocc_android/
├── data/                    # 数据模型
│   ├── ImageItem.kt        # 图片项数据类
│   ├── Session.kt          # 会话数据类
│   └── export/             # 导出相关数据类
│       ├── LayoutConfig.kt # 排版配置
│       └── ExportConfig.kt # 导出配置
├── navigation/             # 导航逻辑
│   └── PhotoCCNavigation.kt
├── repository/             # 数据仓库
│   └── SessionManager.kt   # 会话管理器
├── ui/screens/             # UI 界面
│   ├── HomeScreen.kt       # 主界面
│   ├── DirectorySelectionScreen.kt  # 目录选择界面
│   ├── ImageBrowserScreen.kt        # 图片浏览界面
│   ├── ExportSelectionScreen.kt     # 导出格式选择界面
│   ├── ExportConfigScreen.kt        # 导出配置界面
│   └── LayoutSelectionScreen.kt     # 排版选择界面
├── utils/                  # 工具类
│   ├── DirectorySelector.kt # 目录选择器
│   ├── ImageScanner.kt     # 图片扫描器
│   └── FileSaver.kt        # 文件保存工具
├── export/                 # 导出功能
│   ├── ExportManager.kt    # 导出管理器
│   ├── PdfExporter.kt      # PDF 导出器
│   ├── SingleImageExporter.kt # 单张大图导出器
│   ├── HtmlExporter.kt     # HTML 导出器
│   ├── ZipExporter.kt      # ZIP 导出器
│   └── EpubExporter.kt     # EPUB 导出器
├── viewmodel/              # ViewModel
│   └── PhotoCCViewModel.kt
└── MainActivity.kt         # 主活动
```

## 使用流程

1. **启动应用**: 应用会自动请求必要的存储权限
2. **创建会话**: 点击主界面的"创建新会话"按钮
3. **选择目录**: 使用 SAF 选择一个或多个包含图片的目录
4. **输入会话名称**: 为会话指定一个名称
5. **创建会话**: 应用会扫描选定目录中的所有图片
6. **浏览图片**: 在图片浏览界面中查看图片，支持切换显示模式
7. **导出图片**: 点击导出按钮，选择导出格式和配置
8. **排版设置**: 对于需要排版的格式，配置列数、槽位形状、填充方式等
9. **会话管理**: 在主界面管理已创建的会话

## 支持的图片格式

- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- WebP (.webp)
- BMP (.bmp)

## 显示模式

- **单列模式**: 每行显示一张图片
- **双列模式**: 每行显示两张图片
- **多列模式**: 每行显示三张图片

## 导出功能

### 支持的导出格式
- **PDF 文档**: 根据排版配置生成 PDF 文件
- **单张大图**: 将多张图片按排版合成为一张大图（PNG 格式）
- **HTML 网页**: 生成包含图片的 HTML 文件，支持响应式布局
- **ZIP 压缩包**: 将原始图片文件打包成 ZIP 文件
- **EPUB 电子书**: 生成 EPUB 格式的电子书（简化实现）

### 排版配置选项
- **预设模板**: 4宫格、9宫格、16宫格、单列、双列、三列、自定义
- **槽位形状**: 正方形、圆形、三角形、五角星
- **填充方式**: 居中裁剪、适应居中、拉伸填充
- **样式设置**: 间距、边距、圆角、边框等

### 导出配置选项
- **文件名**: 自定义导出文件名
- **图片质量**: 1-100% 可调节
- **最大尺寸**: 限制图片的最大宽度和高度
- **压缩级别**: ZIP 文件的压缩级别（0-9）

## 权限要求

- `READ_EXTERNAL_STORAGE`: 读取外部存储（Android 12 及以下）
- `READ_MEDIA_IMAGES`: 读取媒体图片（Android 13 及以上）
- `WRITE_EXTERNAL_STORAGE`: 写入外部存储（Android 10 及以下，用于导出文件）

## 构建要求

- Android Studio Arctic Fox 或更高版本
- Kotlin 1.9.0
- Android Gradle Plugin 8.5.1
- 最低 SDK 版本: 29 (Android 10)
- 目标 SDK 版本: 34 (Android 14)

## 构建和运行

1. 克隆项目到本地
2. 使用 Android Studio 打开项目
3. 等待 Gradle 同步完成
4. 连接 Android 设备或启动模拟器
5. 点击运行按钮

## 主要依赖

- Jetpack Compose BOM 2024.04.01
- Navigation Compose 2.7.6
- Lifecycle ViewModel Compose 2.7.0
- Coil Compose 2.5.0
- Accompanist Permissions 0.32.0
- DataStore Preferences 1.0.0
- Kotlinx Serialization JSON 1.6.2
- DocumentFile 1.0.1
- iText7 Core 7.2.5 (PDF 生成)
- HTML2PDF 4.0.5 (HTML 转 PDF)
- ZIP4J 2.11.5 (ZIP 压缩)
- Graphics Core 1.0.0-beta01 (图片处理)

## 注意事项

- 应用需要用户手动授予目录访问权限
- 首次扫描大量图片可能需要一些时间
- 会话数据会自动保存到本地存储
- 支持递归扫描子目录中的图片
- 导出功能需要用户选择保存位置（使用 SAF）
- 大量图片的导出可能需要较长时间
- PDF 和单张大图导出会根据配置调整图片大小
- EPUB 导出为简化实现，实际生成 HTML 格式

## 许可证

本项目仅供学习和参考使用。
